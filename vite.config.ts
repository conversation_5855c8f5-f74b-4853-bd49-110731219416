import react from "@vitejs/plugin-react";
import { copyFileSync, unlinkSync } from "fs";
import { resolve } from "path";
import { defineConfig } from "vite";
import { existsSync, rmSync } from "fs";

const main = "./src/StaffPlanLogin/VoresPlanLogin.html";
const distPath = resolve(__dirname, "dist");
const srcPath = resolve(__dirname, main);
const destPath = resolve(__dirname, "index.html");

const deleteDistFolder = () => {
  if (existsSync(distPath)) {
    rmSync(distPath, { recursive: true });
  }
};

// Rename and copy the file before build
const renameAndMoveFile = () => {
  copyFileSync(srcPath, destPath);
};

// Delete the index.html file after build
const deleteIndexAfterBuild = () => {
  try {
    unlinkSync(destPath);
  } catch (err) {
    console.warn(`error`, err);
  }
};

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    react(),
    {
      name: "rename-and-move-file",
      buildStart: () => {
        deleteDistFolder();
        renameAndMoveFile();
      },
      buildEnd: () => {},
    },
    {
      name: "delete-index-after-build",
      buildEnd: deleteIndexAfterBuild,
    },
  ],
  build: {
    rollupOptions: {
      input: {
        main: "index.html",
      },
      // plugins: [],
    },
  },
  server: {
    open: main,
  },
});

/**
  https://www.freecodecamp.org/news/how-to-import-svgs-in-react-and-vite/
  npm i vite-plugin-svgr
  
  // https://vitejs.dev/config/
  import { defineConfig } from "vite";
  import react from "@vitejs/plugin-react";
  import svgr from "vite-plugin-svgr";
  export default defineConfig({
    plugins: [svgr(), react()],
  });

  // In component:
  import { ReactComponent as Logo } from "./logo.svg";
 */

import React from "react";
import { LoginPageWrapper } from "./LoginPageWrapper";
import { LoginButton } from "./LoginButton";
import { fontSizes, LoginColors } from "./styles";
import styled from "@emotion/styled";

const loginRoleKey = "loginRole";

enum LoginRole {
  Planner = "planner",
  Employee = "employee",
}

/**
 * The `PreLogin` component is responsible for rendering the appropriate login
 * content based on the `loginRole` query parameter in the URL.
 *
 * Depending on the value of `loginRole`, it renders either the `RoleLogin`
 * component with the corresponding role or the `Neither` component if the
 * `loginRole` is not recognized.
 */
export const PreLogin: React.FC = () => {
  const searchParams = new URLSearchParams(window.location.search);
  const loginRole = searchParams.get("loginRole") as LoginRole | null;

  let content: JSX.Element;

  switch (loginRole) {
    case LoginRole.Planner:
      content = <RoleLogin role={LoginRole.Planner} />;
      break;
    case LoginRole.Employee:
      content = <RoleLogin role={LoginRole.Employee} />;
      break;
    default:
      content = <Neither />;
  }

  return <LoginPageWrapper>{content}</LoginPageWrapper>;
};

const roleLabels = {
  [LoginRole.Planner]: "planlægger",
  [LoginRole.Employee]: "medarbejder",
};

/**
 * Component for handling role-based login functionality.
 * This component displays a message indicating the current login role and provides
 * a link to switch to the other role. It also includes a login button.
 */
const RoleLogin = ({ role }: { role: LoginRole }) => {
  const paramValue =
    role === LoginRole.Planner ? LoginRole.Employee : LoginRole.Planner;
  return (
    <>
      <RoleSwitchDiv>
        <Medium>Du er ved at logge ind som {roleLabels[role]}</Medium> <br />
        <a href={`?${loginRoleKey}=${paramValue}`}>
          Skift til{" "}
          {role === LoginRole.Planner
            ? roleLabels[LoginRole.Employee]
            : roleLabels[LoginRole.Planner]}
        </a>
      </RoleSwitchDiv>
      <LoginButton>Log ind</LoginButton>
    </>
  );
};

const RoleSwitchDiv = styled.div`
  color: ${LoginColors.text.darkest};
  margin-bottom: 20px;
  line-height: 2;
`;

const Neither = () => {
  return (
    <>
      <LoginButton>Log på som planlægger</LoginButton>
      <LoginButton>Log på som medarbejder</LoginButton>
    </>
  );
};

const Medium = styled.span`
  font-size: ${fontSizes.medium};
`;

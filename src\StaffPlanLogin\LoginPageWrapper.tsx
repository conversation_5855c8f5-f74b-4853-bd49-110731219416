import styled from "@emotion/styled";
import React, { PropsWithChildren } from "react";
import "./basetyles.css";
import { fontSizes, LoginColors } from "./styles";
import PdcLogo from "./PdcLogo.svg";
import VoresPlanSvg from "./VoresPlanText.svg";

/**
 * `LoginPageWrapper` is a React functional component that serves as a wrapper for the login page.
 * It arranges its children and other elements in a grid layout.
 * The layout includes:
 * - A logo in the upper left corner.
 * - A privacy policy link in the upper right corner.
 * - A welcome message and logo in the center.
 * - A footer with a contact link at the bottom.
 */
export const LoginPageWrapper: React.FC<PropsWithChildren> = ({ children }) => {
  return (
    <PageWrap>
      <GridCell className="row1 col1">
        <img className="UpperLeft" src={PdcLogo} width="120" height="38" />
      </GridCell>
      <GridCell className="row1 col3">
        <div className="UpperRight">
          <a
            href="https://www.pdc.com/privacy-policy/privacy-policy-for-pdc-customer-portal/"
            target="_blank"
          >
            Privalivs- og cookiepolitik
          </a>
        </div>
      </GridCell>
      <GridCell className="row2 col2">
        <h4>Velkommen til</h4>
        <VoresPlanLogo />
        <ContentWrap>{children}</ContentWrap>
      </GridCell>
      <GridCell className="row3 col2">
        <div className="loginFooter">
          Hvis du oplever problemer, så{" "}
          <a href="https://www.pdc.com/contact-pdc/" target="_blank">
            kontakt PDC
          </a>
        </div>
      </GridCell>
    </PageWrap>
  );
};

/**
 * `PageWrap` is a styled component that serves as a wrapper for the login page.
 * It uses a CSS grid layout with three columns and centers its content.
 */
const PageWrap = styled.div`
  display: grid;
  grid-template-columns: auto minmax(300px, 40%) auto;
  text-align: center;

  h4 {
    font-size: ${fontSizes.basic};
    font-weight: 500;
    color: ${LoginColors.text.darkGray};
    margin-bottom: 0;
  }

  .loginFooter {
    font-size: 12px;
    color: ${LoginColors.text.darkGray};
    margin-top: 30px;
    position: absolute;
    bottom: 25px;
    left: 50%;
    transform: translateX(-50%);
  }

  & a:link,
  a:visited {
    color: ${LoginColors.text.hyperLink};
  }
`;

/**
 * `GridCell` is a styled `div` component that defines a grid cell with specific styles.
 */
const GridCell = styled.div`
  /* Border for debugging grid: */
  /* border: 1px dotted #e0e0e0; */
  min-height: 2rem;
  &.row1 {
    grid-row: 1;
    height: 140px;
  }
  &.row2 {
    grid-row: 2;
    height: calc(100vh - 260px);
  }
  &.row3 {
    grid-row: 3;
    height: 100px;
  }
  &.col1 {
    grid-column: 1;
  }
  &.col2 {
    grid-column: 2;
  }
  &.col3 {
    grid-column: 3;
  }
  .UpperLeft {
    position: absolute;
    top: 10px;
    left: 10px;
  }
  .UpperRight {
    position: absolute;
    top: 10px;
    right: 10px;
  }
`;

const ContentWrap = styled.div`
  width: 80%;
  margin: 60px auto 20px auto;
`;

/**
 * A functional component that renders the VoresPlan SVG logo.
 */
const VoresPlanLogo = () => {
  return <img src={VoresPlanSvg} alt="VoresPlan Text Logo" height={60} />;
};
